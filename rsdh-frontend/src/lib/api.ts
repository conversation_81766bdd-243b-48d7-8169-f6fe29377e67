// API client for backend communication
const API_BASE_URL = 'http://localhost:3001/api';

// Token management
export const tokenManager = {
  getToken: (): string | null => {
    return localStorage.getItem('auth_token');
  },

  setToken: (token: string): void => {
    localStorage.setItem('auth_token', token);
  },

  removeToken: (): void => {
    localStorage.removeItem('auth_token');
  }
};

// API client class
class ApiClient {
  private baseURL: string;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    const token = tokenManager.getToken();

    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  // GET request
  async get<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'GET' });
  }

  // POST request
  async post<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  // PUT request
  async put<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  // PATCH request
  async patch<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  // DELETE request
  async delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }

  // Payment methods
  async createWeChatPayment(paymentId: string, data: PaymentRequest): Promise<PaymentResponse> {
    return this.post<PaymentResponse>(`/payments/${paymentId}/wechat`, data);
  }

  async getPaymentStatus(paymentId: string): Promise<PaymentStatus> {
    return this.get<PaymentStatus>(`/payments/${paymentId}/status`);
  }

  async confirmAppointment(appointmentId: string, token: string): Promise<{ success: boolean; message: string }> {
    return this.get<{ success: boolean; message: string }>(`/appointments/${appointmentId}/confirm?token=${token}`);
  }

  async rejectAppointment(appointmentId: string, token: string): Promise<{ success: boolean; message: string }> {
    return this.get<{ success: boolean; message: string }>(`/appointments/${appointmentId}/reject?token=${token}`);
  }
}

// Create API client instance
export const apiClient = new ApiClient(API_BASE_URL);

// API response types
export interface ApiResponse<T> {
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// User types
export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  role: string;
  emailVerified: boolean;
  disabled: boolean;
  createdAt: string;
  updatedAt: string;
}

// Auth types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  message: string;
  token: string;
  user: {
    id: string;
    email: string;
    name: string;
    role: string;
  };
}

export interface RegisterRequest {
  email: string;
  code: string;
  password: string;
  name: string;
  avatar?: string;
}

export interface SendCodeRequest {
  email: string;
  type: 'registration' | 'password-reset' | 'email-verification';
}

export interface ResetPasswordRequest {
  email: string;
  code: string;
  newPassword: string;
}

// Tutor types
export interface Tutor {
  id: string;
  userId: string;
  title?: string;
  bio?: string;
  rate: string; // Rate in yuan as string
  hourlyRate?: string; // Hourly rate in yuan as string
  halfHourRate?: string; // Half hour rate in yuan as string
  isFree?: boolean; // Whether tutor provides free service
  currency?: string; // Currency code
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  user: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
  };
  education: TutorEducation[];
  career: TutorCareer[];
  availability: TutorAvailability[];
  reviews: Review[];
  averageRating?: number;
  totalReviews?: number;
}

export interface TutorEducation {
  id: string;
  tutorId: string;
  degree: string;
  fieldOfStudy: string;
  institution: string;
  startYear: number;
  endYear?: number;
  description?: string;
}

export interface TutorCareer {
  id: string;
  tutorId: string;
  company: string;
  position: string;
  startDate: string;
  endDate?: string;
  isCurrent: boolean;
  description?: string;
}

export interface TutorAvailability {
  id: string;
  tutorId: string;
  dayOfWeek: number;
  startTime: string;
  endTime: string;
}

export interface TutorApplication {
  title?: string;
  bio?: string;
  rate: number;
  education: Omit<TutorEducation, 'id' | 'tutorId'>[];
  career: Omit<TutorCareer, 'id' | 'tutorId'>[];
}

// Appointment types
export interface Appointment {
  id: string;
  tutorId: string;
  studentId: string;
  startTime: string;
  endTime: string;
  meetingType: 'online' | 'in_person';
  meetingLink?: string;
  notes?: string;
  status: 'scheduled' | 'completed' | 'cancelled';
  createdAt: string;
  updatedAt: string;
  tutor?: Tutor;
  student?: User;
}

export interface CreateAppointmentRequest {
  tutorId: string;
  startTime: string;
  endTime: string;
  meetingType: 'online' | 'in_person';
  meetingLink?: string;
  notes?: string;
}

export interface CreateAppointmentResponse {
  appointment: Appointment;
  requiresPayment: boolean;
  paymentId?: string;
  price: string; // Price in yuan as string
}

export interface PaymentRequest {
  openid?: string;
}

export interface PaymentResponse {
  prepayId: string;
  paySign: string;
  timeStamp: string;
  nonceStr: string;
  package: string;
  signType: string;
}

export interface PaymentStatus {
  id: string;
  status: string;
  amount: string; // Amount in yuan as string
  currency: string;
  paidAt?: string;
}

// Review types
export interface Review {
  id: string;
  appointmentId: string;
  tutorId: string;
  studentId: string;
  rating: number;
  comment?: string;
  createdAt: string;
  student?: User;
  tutor?: Tutor;
}

// Verification Material types
export interface VerificationMaterial {
  id: string;
  materialType: string;
  description?: string;
  status: 'pending' | 'approved' | 'rejected';
  file: {
    id: string;
    originalName: string;
    size: number;
    mimeType: string;
  };
  reviewNotes?: string;
  reviewedAt?: string;
  reviewedBy?: {
    id: string;
    name: string;
    email: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface CreateEducationVerificationRequest {
  educationId: string;
  fileId: string;
  materialType: string;
  description?: string;
}

export interface CreateCareerVerificationRequest {
  careerId: string;
  fileId: string;
  materialType: string;
  description?: string;
}

export interface UpdateVerificationStatusRequest {
  status: 'approved' | 'rejected';
  reviewNotes?: string;
}

export interface TutorVerifications {
  educationVerifications: VerificationMaterial[];
  careerVerifications: VerificationMaterial[];
}

export interface PendingVerifications {
  educationVerifications: (VerificationMaterial & {
    education: {
      degree: string;
      institution: string;
      tutor: {
        user: {
          id: string;
          name: string;
          email: string;
        };
      };
    };
  })[];
  careerVerifications: (VerificationMaterial & {
    career: {
      title: string;
      company: string;
      tutor: {
        user: {
          id: string;
          name: string;
          email: string;
        };
      };
    };
  })[];
}
